@echo off
echo Installing Tesseract OCR for Windows...

REM Check if chocolatey is available
choco --version >nul 2>&1
if %errorlevel% == 0 (
    echo Installing Tesseract via Chocolatey...
    choco install tesseract
) else (
    echo Chocolatey not found. Please install Tesseract manually:
    echo 1. Download from: https://github.com/UB-Mannheim/tesseract/wiki
    echo 2. Install and add to PATH
    echo 3. Or use: winget install UB-Mannheim.TesseractOCR
)

echo.
echo Testing Tesseract installation...
tesseract --version
if %errorlevel% == 0 (
    echo ✅ Tesseract installed successfully!
) else (
    echo ❌ Tesseract not found in PATH. Please install manually.
)

pause
