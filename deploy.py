#!/usr/bin/env python3
"""
Deployment Script for A2A OCR System

Automated deployment and setup script for the A2A OCR system.
Handles dependency installation, configuration, and service startup.

Features:
- Dependency management
- Configuration validation
- Service health checks
- Environment setup
- Development vs production deployment

Usage:
    python deploy.py --setup
    python deploy.py --start-services
    python deploy.py --check-health
    python deploy.py --full-deploy
"""

import argparse
import json
import logging
import os
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional

import httpx

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class A2AOCRDeployer:
    """Deployment manager for A2A OCR system."""
    
    def __init__(self, 
                 base_dir: Optional[str] = None,
                 config_file: str = "deploy_config.json"):
        """
        Initialize the deployer.
        
        Args:
            base_dir: Base directory for deployment
            config_file: Configuration file path
        """
        self.base_dir = Path(base_dir) if base_dir else Path.cwd()
        self.config_file = self.base_dir / config_file
        self.config = self.load_config()
        
    def load_config(self) -> Dict:
        """Load deployment configuration."""
        default_config = {
            "services": {
                "fastapi_server": {
                    "host": "0.0.0.0",
                    "port": 8005,
                    "ocr_server_url": "http://localhost:8003",
                    "log_level": "info"
                },
                "ocr_agent": {
                    "url": "http://localhost:8003",
                    "timeout": 30
                }
            },
            "dependencies": {
                "requirements_file": "requirements_fastapi_server.txt",
                "python_version": "3.8"
            },
            "deployment": {
                "environment": "development",
                "validate_files": True,
                "max_file_size": 10485760,
                "enable_cors": True
            }
        }
        
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                # Merge with defaults
                default_config.update(config)
                return default_config
            except Exception as e:
                logger.warning(f"Failed to load config file: {e}. Using defaults.")
                
        return default_config
        
    def save_config(self):
        """Save current configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            logger.info(f"Configuration saved to {self.config_file}")
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            
    def check_python_version(self) -> bool:
        """Check if Python version meets requirements."""
        required_version = self.config["dependencies"]["python_version"]
        current_version = f"{sys.version_info.major}.{sys.version_info.minor}"
        
        if current_version >= required_version:
            logger.info(f"Python version {current_version} meets requirements (>= {required_version})")
            return True
        else:
            logger.error(f"Python version {current_version} does not meet requirements (>= {required_version})")
            return False
            
    def install_dependencies(self) -> bool:
        """Install Python dependencies."""
        requirements_file = self.base_dir / self.config["dependencies"]["requirements_file"]
        
        if not requirements_file.exists():
            logger.error(f"Requirements file not found: {requirements_file}")
            return False
            
        try:
            logger.info("Installing Python dependencies...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], capture_output=True, text=True, check=True)
            
            logger.info("Dependencies installed successfully")
            logger.debug(f"pip output: {result.stdout}")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to install dependencies: {e}")
            logger.error(f"pip error: {e.stderr}")
            return False
            
    def validate_files(self) -> bool:
        """Validate required files exist."""
        required_files = [
            "a2a_ocr_client.py",
            "fastapi_ocr_server.py",
            "file_utils.py",
            "requirements_fastapi_server.txt"
        ]
        
        missing_files = []
        for file_name in required_files:
            file_path = self.base_dir / file_name
            if not file_path.exists():
                missing_files.append(file_name)
                
        if missing_files:
            logger.error(f"Missing required files: {missing_files}")
            return False
            
        logger.info("All required files present")
        return True
        
    async def check_ocr_agent_health(self) -> bool:
        """Check if OCR agent server is running and healthy."""
        ocr_url = self.config["services"]["ocr_agent"]["url"]
        timeout = self.config["services"]["ocr_agent"]["timeout"]
        
        try:
            async with httpx.AsyncClient(timeout=timeout) as client:
                # Try to connect to the OCR agent
                response = await client.get(f"{ocr_url}/health")
                if response.status_code == 200:
                    logger.info(f"OCR agent server is healthy at {ocr_url}")
                    return True
                else:
                    logger.warning(f"OCR agent server returned status {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to connect to OCR agent at {ocr_url}: {e}")
            return False
            
    def start_fastapi_server(self, background: bool = False) -> Optional[subprocess.Popen]:
        """Start the FastAPI server."""
        config = self.config["services"]["fastapi_server"]
        
        cmd = [
            sys.executable, "fastapi_ocr_server.py",
            "--host", config["host"],
            "--port", str(config["port"]),
            "--ocr-server", config["ocr_server_url"],
            "--log-level", config["log_level"]
        ]
        
        if not self.config["deployment"]["validate_files"]:
            cmd.append("--no-validate")
            
        try:
            if background:
                logger.info(f"Starting FastAPI server in background on {config['host']}:{config['port']}")
                process = subprocess.Popen(cmd, cwd=self.base_dir)
                return process
            else:
                logger.info(f"Starting FastAPI server on {config['host']}:{config['port']}")
                subprocess.run(cmd, cwd=self.base_dir, check=True)
                return None
                
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to start FastAPI server: {e}")
            return None
            
    async def check_fastapi_health(self) -> bool:
        """Check if FastAPI server is running and healthy."""
        config = self.config["services"]["fastapi_server"]
        url = f"http://{config['host']}:{config['port']}/health"
        
        try:
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get(url)
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"FastAPI server is healthy at {url}")
                    logger.info(f"Server status: {data.get('status', 'unknown')}")
                    return True
                else:
                    logger.warning(f"FastAPI server returned status {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to connect to FastAPI server at {url}: {e}")
            return False
            
    async def run_health_checks(self) -> bool:
        """Run comprehensive health checks."""
        logger.info("Running health checks...")
        
        checks = [
            ("Python version", self.check_python_version()),
            ("Required files", self.validate_files()),
            ("OCR agent", await self.check_ocr_agent_health()),
            ("FastAPI server", await self.check_fastapi_health())
        ]
        
        all_passed = True
        for check_name, result in checks:
            if result:
                logger.info(f"✓ {check_name}: PASS")
            else:
                logger.error(f"✗ {check_name}: FAIL")
                all_passed = False
                
        return all_passed
        
    def generate_test_data(self):
        """Generate test data for the system."""
        try:
            logger.info("Generating test data...")
            result = subprocess.run([
                sys.executable, "test_ocr_system.py", "--generate-test-data"
            ], cwd=self.base_dir, capture_output=True, text=True, check=True)
            
            logger.info("Test data generated successfully")
            logger.debug(f"Test output: {result.stdout}")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to generate test data: {e}")
            logger.error(f"Test error: {e.stderr}")
            
    async def full_deployment(self):
        """Run full deployment process."""
        logger.info("Starting full deployment process...")
        
        # Step 1: Validate environment
        if not self.check_python_version():
            logger.error("Python version check failed. Aborting deployment.")
            return False
            
        if not self.validate_files():
            logger.error("File validation failed. Aborting deployment.")
            return False
            
        # Step 2: Install dependencies
        if not self.install_dependencies():
            logger.error("Dependency installation failed. Aborting deployment.")
            return False
            
        # Step 3: Check OCR agent
        if not await self.check_ocr_agent_health():
            logger.warning("OCR agent health check failed. Server may not be running.")
            logger.info("Please ensure the OCR agent server is running before using the system.")
            
        # Step 4: Generate test data
        self.generate_test_data()
        
        # Step 5: Save configuration
        self.save_config()
        
        logger.info("Deployment completed successfully!")
        logger.info("Next steps:")
        logger.info("1. Start the FastAPI server: python deploy.py --start-server")
        logger.info("2. Run health checks: python deploy.py --check-health")
        logger.info("3. Test the API: Import postman_collection.json into Postman")
        
        return True

async def main():
    """Main deployment function."""
    parser = argparse.ArgumentParser(description="A2A OCR System Deployment")
    parser.add_argument("--setup", action="store_true", help="Setup dependencies and environment")
    parser.add_argument("--start-server", action="store_true", help="Start FastAPI server")
    parser.add_argument("--start-server-bg", action="store_true", help="Start FastAPI server in background")
    parser.add_argument("--check-health", action="store_true", help="Run health checks")
    parser.add_argument("--generate-test-data", action="store_true", help="Generate test data")
    parser.add_argument("--full-deploy", action="store_true", help="Run full deployment process")
    parser.add_argument("--config", default="deploy_config.json", help="Configuration file path")
    parser.add_argument("--base-dir", help="Base directory for deployment")
    
    args = parser.parse_args()
    
    # Initialize deployer
    deployer = A2AOCRDeployer(base_dir=args.base_dir, config_file=args.config)
    
    if args.setup:
        success = deployer.install_dependencies()
        if success:
            logger.info("Setup completed successfully!")
        else:
            logger.error("Setup failed!")
            sys.exit(1)
            
    elif args.start_server:
        deployer.start_fastapi_server(background=False)
        
    elif args.start_server_bg:
        process = deployer.start_fastapi_server(background=True)
        if process:
            logger.info(f"FastAPI server started with PID: {process.pid}")
            # Wait a moment for server to start
            time.sleep(2)
            if await deployer.check_fastapi_health():
                logger.info("Server started successfully!")
            else:
                logger.warning("Server may not have started properly")
        else:
            logger.error("Failed to start server")
            
    elif args.check_health:
        success = await deployer.run_health_checks()
        if success:
            logger.info("All health checks passed!")
        else:
            logger.error("Some health checks failed!")
            sys.exit(1)
            
    elif args.generate_test_data:
        deployer.generate_test_data()
        
    elif args.full_deploy:
        success = await deployer.full_deployment()
        if not success:
            sys.exit(1)
            
    else:
        parser.print_help()

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
