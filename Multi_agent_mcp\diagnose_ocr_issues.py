#!/usr/bin/env python3
"""
OCR System Diagnostic Script

This script diagnoses common issues with the A2A OCR system and provides solutions.
"""

import sys
import subprocess
import json
import base64
import os
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont


def check_python_environment():
    """Check Python environment and required packages."""
    print("🔍 Checking Python Environment...")
    print(f"Python version: {sys.version}")
    
    required_packages = [
        "pytesseract",
        "PIL",
        "httpx",
        "mcp_ocr"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is available")
        except ImportError:
            print(f"❌ {package} is missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📦 Install missing packages:")
        print(f"pip install {' '.join(missing_packages)}")
    
    return len(missing_packages) == 0


def check_tesseract():
    """Check if Tesseract OCR is properly installed."""
    print("\n🔍 Checking Tesseract OCR...")
    
    try:
        import pytesseract
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract version: {version}")
        return True
    except Exception as e:
        print(f"❌ Tesseract error: {e}")
        print("\n🔧 To fix Tesseract issues:")
        print("1. Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. Or use: winget install UB-Mannheim.TesseractOCR")
        print("3. Or use: choco install tesseract")
        print("4. Make sure Tesseract is in your PATH")
        return False


def test_mcp_ocr_tool():
    """Test the MCP OCR tool directly."""
    print("\n🔍 Testing MCP OCR Tool...")
    
    try:
        # Create a simple test image
        img = Image.new('RGB', (200, 100), color='white')
        draw = ImageDraw.Draw(img)
        draw.text((10, 30), "TEST OCR", fill='black')
        img.save('temp_test_image.jpg', 'JPEG')
        
        # Encode image
        with open('temp_test_image.jpg', 'rb') as f:
            image_data = base64.b64encode(f.read()).decode('utf-8')
        
        # Test MCP protocol
        init_request = {
            'jsonrpc': '2.0',
            'id': 1,
            'method': 'initialize',
            'params': {
                'protocolVersion': '2024-11-05',
                'capabilities': {},
                'clientInfo': {'name': 'diagnostic-client', 'version': '1.0.0'}
            }
        }
        
        # Start MCP process
        proc = subprocess.Popen(
            ['python', '-m', 'mcp_ocr'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Send initialization
        proc.stdin.write(json.dumps(init_request) + '\n')
        proc.stdin.flush()
        
        # Read response
        response = proc.stdout.readline()
        init_response = json.loads(response.strip())
        
        if 'result' in init_response:
            print("✅ MCP OCR tool initialization successful")
            
            # Try to list tools
            tools_request = {
                'jsonrpc': '2.0',
                'id': 2,
                'method': 'tools/list',
                'params': {}
            }
            
            proc.stdin.write(json.dumps(tools_request) + '\n')
            proc.stdin.flush()
            
            response = proc.stdout.readline()
            tools_response = json.loads(response.strip())
            
            if 'result' in tools_response:
                print("✅ MCP tools list successful")
                tools = tools_response['result'].get('tools', [])
                for tool in tools:
                    print(f"   Tool: {tool.get('name', 'unknown')}")
            else:
                print(f"⚠️  MCP tools list failed: {tools_response}")
        else:
            print(f"❌ MCP initialization failed: {init_response}")
        
        proc.stdin.close()
        proc.wait()
        
        # Clean up
        if os.path.exists('temp_test_image.jpg'):
            os.remove('temp_test_image.jpg')
        
        return 'result' in init_response
        
    except Exception as e:
        print(f"❌ MCP OCR tool test failed: {e}")
        return False


def test_direct_ocr():
    """Test direct OCR functionality."""
    print("\n🔍 Testing Direct OCR...")
    
    try:
        import pytesseract
        from PIL import Image
        
        # Create test image
        img = Image.new('RGB', (200, 100), color='white')
        draw = ImageDraw.Draw(img)
        draw.text((10, 30), "DIRECT OCR TEST", fill='black')
        img.save('temp_direct_test.jpg', 'JPEG')
        
        # Test OCR
        text = pytesseract.image_to_string(img)
        print(f"✅ Direct OCR result: '{text.strip()}'")
        
        # Clean up
        if os.path.exists('temp_direct_test.jpg'):
            os.remove('temp_direct_test.jpg')
        
        return True
        
    except Exception as e:
        print(f"❌ Direct OCR test failed: {e}")
        return False


def test_a2a_connection():
    """Test A2A connection to OCR agent."""
    print("\n🔍 Testing A2A Connection...")
    
    try:
        import httpx
        
        # Test agent card
        with httpx.Client() as client:
            response = client.get("http://localhost:8003/.well-known/agent.json")
            if response.status_code == 200:
                agent_card = response.json()
                print("✅ Agent card accessible")
                print(f"   Agent: {agent_card.get('name', 'unknown')}")
                print(f"   Version: {agent_card.get('version', 'unknown')}")
            else:
                print(f"❌ Agent card not accessible: {response.status_code}")
                return False
        
        # Test RPC endpoint
        test_payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "send_message",
            "params": {
                "message": {
                    "content": [{"type": "text", "text": "test"}],
                    "role": "user"
                }
            }
        }
        
        with httpx.Client() as client:
            response = client.post(
                "http://localhost:8003/a2a/ocr_agent",
                json=test_payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                print("✅ A2A RPC endpoint accessible")
                result = response.json()
                if 'error' not in result:
                    print("✅ A2A communication successful")
                else:
                    print(f"⚠️  A2A returned error: {result.get('error', {}).get('message', 'unknown')}")
            else:
                print(f"❌ A2A RPC endpoint error: {response.status_code}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ A2A connection test failed: {e}")
        return False


def main():
    """Run all diagnostic tests."""
    print("🔧 OCR System Diagnostic Tool")
    print("=" * 50)
    
    results = {
        "python_env": check_python_environment(),
        "tesseract": check_tesseract(),
        "mcp_ocr": test_mcp_ocr_tool(),
        "direct_ocr": test_direct_ocr(),
        "a2a_connection": test_a2a_connection()
    }
    
    print("\n📊 Diagnostic Summary:")
    print("=" * 50)
    
    for test, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test.replace('_', ' ').title()}: {status}")
    
    if all(results.values()):
        print("\n🎉 All tests passed! Your OCR system should be working.")
    else:
        print("\n🔧 Some tests failed. Please address the issues above.")
        
        if not results["tesseract"]:
            print("\n💡 Recommendation: Install Tesseract OCR first")
        elif not results["mcp_ocr"] and results["direct_ocr"]:
            print("\n💡 Recommendation: Use direct OCR agent instead of MCP-based agent")
        elif not results["a2a_connection"]:
            print("\n💡 Recommendation: Make sure OCR agent server is running")


if __name__ == "__main__":
    main()
