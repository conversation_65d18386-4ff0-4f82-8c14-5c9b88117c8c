# A2A OCR System

A comprehensive Agent-to-Agent (A2A) OCR system with HTTP API wrapper for seamless file processing and text extraction.

## Overview

This system implements a two-layer architecture for OCR processing:

- **Layer 1**: HTTP API server (FastAPI) that receives files via multipart form-data from HTTP clients like Postman
- **Layer 2**: A2A client that communicates with the OCR agent server using the A2A protocol

## Features

### Core Capabilities
- **Multi-format Support**: JPG, JPEG, PNG, PDF, TIFF, BMP, GIF, WEBP, SVG
- **File Validation**: Size limits, format verification, signature validation
- **Batch Processing**: Handle multiple files in a single request
- **Error Handling**: Comprehensive error reporting and logging
- **Context Management**: Support for conversation continuity via context IDs

### API Features
- **RESTful API**: FastAPI-based HTTP endpoints with automatic documentation
- **File Upload**: Multipart form-data support for file uploads
- **Progress Tracking**: Task IDs and processing time tracking
- **Health Monitoring**: Health check endpoints for system status

### Client Features
- **Enhanced A2A Client**: Improved file handling and validation
- **Directory Scanning**: Recursive directory processing
- **CLI Interface**: Command-line tool for direct usage
- **Async Processing**: Efficient async/await implementation

## Architecture

```
HTTP Client (Postman) 
    ↓ (multipart/form-data)
FastAPI Server (Layer 1)
    ↓ (A2A protocol)
A2A OCR Client (Layer 2)
    ↓ (JSON-RPC)
OCR Agent Server
```

## Installation

### Prerequisites
- Python 3.8+
- OCR Agent Server running (typically on port 8003)
- Required Python packages (see requirements)

### Setup
1. **Install dependencies**:
   ```bash
   pip install -r requirements_fastapi_server.txt
   ```

2. **Verify OCR Agent Server**:
   Ensure the OCR agent server is running at `http://localhost:8003` (or your configured URL).

3. **Start FastAPI Server**:
   ```bash
   python fastapi_ocr_server.py --port 8005
   ```

## Usage

### HTTP API Usage

#### Start the Server
```bash
# Basic usage
python fastapi_ocr_server.py

# Custom configuration
python fastapi_ocr_server.py --host 0.0.0.0 --port 8005 --ocr-server http://localhost:8003
```

#### API Endpoints

**Health Check**
```http
GET /health
```

**File Upload**
```http
POST /upload
Content-Type: multipart/form-data

files: [file1.jpg, file2.png, ...]
user_id: optional_user_id
context_id: optional_context_id
description: optional_description
include_file_data: false
```

#### Example with curl
```bash
curl -X POST "http://localhost:8005/upload" \
  -F "files=@image1.jpg" \
  -F "files=@image2.png" \
  -F "user_id=test_user" \
  -F "description=OCR processing test"
```

### A2A Client Usage

#### Command Line Interface
```bash
# Process single image
python a2a_ocr_client.py -i image.jpg

# Process multiple images
python a2a_ocr_client.py -i image1.jpg -i image2.png

# Process directory
python a2a_ocr_client.py -d ./images --recursive

# Save results to file
python a2a_ocr_client.py -i image.jpg -o results.json

# Custom server URL
python a2a_ocr_client.py -i image.jpg -s http://localhost:8003
```

#### Programmatic Usage
```python
import asyncio
from a2a_ocr_client import A2AOCRClient

async def process_images():
    async with A2AOCRClient(server_url="http://localhost:8003") as client:
        result = await client.process_images(["image1.jpg", "image2.png"])
        print(result)

asyncio.run(process_images())
```

## File Support

### Supported Formats
- **Images**: JPG, JPEG, PNG, BMP, GIF, WEBP, TIFF, TIF
- **Documents**: PDF
- **Vector Graphics**: SVG

### File Limits
- **Maximum Size**: 10MB per file
- **Minimum Size**: 100 bytes
- **Batch Limit**: No hard limit (memory dependent)

### Validation Features
- File signature verification
- MIME type detection
- Size validation
- Format compatibility checking

## Testing

### Generate Test Data
```bash
python test_ocr_system.py --generate-test-data
```

### Run Tests
```bash
# All tests
python test_ocr_system.py --run-all

# Unit tests only
python test_ocr_system.py --test-client

# Integration tests
python test_ocr_system.py --integration
```

### Postman Testing
1. Import `postman_collection.json` into Postman
2. Set the `base_url` variable to your server URL
3. Run the collection tests

## Configuration

### Environment Variables
```bash
# Server configuration
export OCR_SERVER_URL="http://localhost:8003"
export API_SERVER_PORT="8005"
export API_SERVER_HOST="0.0.0.0"

# File processing
export MAX_FILE_SIZE="10485760"  # 10MB
export VALIDATE_FILES="true"

# Logging
export LOG_LEVEL="INFO"
```

### Server Configuration
```python
# Custom FastAPI app
from fastapi_ocr_server import create_app

app = create_app(
    ocr_server_url="http://localhost:8003",
    validate_files=True
)
```

## API Documentation

When the FastAPI server is running, visit:
- **Swagger UI**: http://localhost:8005/docs
- **ReDoc**: http://localhost:8005/redoc

## Error Handling

### Common Error Codes
- **400**: Bad Request (invalid files, missing parameters)
- **413**: Request Entity Too Large (file size exceeded)
- **422**: Unprocessable Entity (validation errors)
- **500**: Internal Server Error (processing failures)

### Error Response Format
```json
{
  "status": "error",
  "task_id": "uuid",
  "user_id": "user_id",
  "context_id": null,
  "processing_time": 1.23,
  "file_count": 1,
  "results": {},
  "error": "Error description"
}
```

## Performance

### Optimization Tips
- Use batch processing for multiple files
- Enable file validation for security
- Monitor memory usage with large files
- Use appropriate timeout settings

### Monitoring
- Health check endpoint: `/health`
- Processing time tracking in responses
- Comprehensive logging with configurable levels

## Development

### Project Structure
```
├── a2a_ocr_client.py          # Enhanced A2A client
├── fastapi_ocr_server.py      # HTTP API server
├── file_utils.py              # File processing utilities
├── test_ocr_system.py         # Test suite
├── postman_collection.json    # API testing collection
├── requirements_fastapi_server.txt  # Dependencies
└── A2A_OCR_SYSTEM_README.md   # This file
```

### Contributing
1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Test with the provided test suite

## Troubleshooting

### Common Issues

**Server won't start**
- Check if port is already in use
- Verify OCR agent server is running
- Check Python dependencies

**File upload fails**
- Verify file format is supported
- Check file size limits
- Ensure proper multipart encoding

**OCR processing errors**
- Verify OCR agent server connectivity
- Check A2A client configuration
- Review server logs for details

### Logging
Enable debug logging for detailed troubleshooting:
```bash
python fastapi_ocr_server.py --log-level debug
```

## License

This project is part of the A2A OCR system implementation.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the API documentation
3. Run the test suite to verify setup
4. Check server logs for error details
