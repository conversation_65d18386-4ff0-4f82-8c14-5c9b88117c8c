# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import datetime
import json
import logging
import time
from typing import Any, Optional, Union
from urllib.parse import urlencode

from google.genai import _api_module
from google.genai import _common
from google.genai._common import get_value_by_path as getv
from google.genai._common import set_value_by_path as setv

from . import types


logger = logging.getLogger("vertexai_genai.promptoptimizer")


def _OptimizeRequestParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _CustomJobSpec_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["base_output_directory"]) is not None:
        setv(
            to_object,
            ["baseOutputDirectory"],
            getv(from_object, ["base_output_directory"]),
        )

    if getv(from_object, ["enable_dashboard_access"]) is not None:
        setv(
            to_object,
            ["enableDashboardAccess"],
            getv(from_object, ["enable_dashboard_access"]),
        )

    if getv(from_object, ["enable_web_access"]) is not None:
        setv(
            to_object,
            ["enableWebAccess"],
            getv(from_object, ["enable_web_access"]),
        )

    if getv(from_object, ["experiment"]) is not None:
        setv(to_object, ["experiment"], getv(from_object, ["experiment"]))

    if getv(from_object, ["experiment_run"]) is not None:
        setv(to_object, ["experimentRun"], getv(from_object, ["experiment_run"]))

    if getv(from_object, ["models"]) is not None:
        setv(to_object, ["models"], getv(from_object, ["models"]))

    if getv(from_object, ["network"]) is not None:
        setv(to_object, ["network"], getv(from_object, ["network"]))

    if getv(from_object, ["persistent_resource_id"]) is not None:
        setv(
            to_object,
            ["persistentResourceId"],
            getv(from_object, ["persistent_resource_id"]),
        )

    if getv(from_object, ["protected_artifact_location_id"]) is not None:
        setv(
            to_object,
            ["protectedArtifactLocationId"],
            getv(from_object, ["protected_artifact_location_id"]),
        )

    if getv(from_object, ["psc_interface_config"]) is not None:
        setv(
            to_object,
            ["pscInterfaceConfig"],
            getv(from_object, ["psc_interface_config"]),
        )

    if getv(from_object, ["reserved_ip_ranges"]) is not None:
        setv(
            to_object,
            ["reservedIpRanges"],
            getv(from_object, ["reserved_ip_ranges"]),
        )

    if getv(from_object, ["scheduling"]) is not None:
        setv(to_object, ["scheduling"], getv(from_object, ["scheduling"]))

    if getv(from_object, ["service_account"]) is not None:
        setv(
            to_object,
            ["serviceAccount"],
            getv(from_object, ["service_account"]),
        )

    if getv(from_object, ["tensorboard"]) is not None:
        setv(to_object, ["tensorboard"], getv(from_object, ["tensorboard"]))

    if getv(from_object, ["worker_pool_specs"]) is not None:
        setv(
            to_object,
            ["workerPoolSpecs"],
            getv(from_object, ["worker_pool_specs"]),
        )

    return to_object


def _CustomJob_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["display_name"]) is not None:
        setv(parent_object, ["displayName"], getv(from_object, ["display_name"]))

    if getv(from_object, ["job_spec"]) is not None:
        setv(
            parent_object,
            ["jobSpec"],
            _CustomJobSpec_to_vertex(getv(from_object, ["job_spec"]), to_object),
        )

    if getv(from_object, ["create_time"]) is not None:
        setv(to_object, ["createTime"], getv(from_object, ["create_time"]))

    if getv(from_object, ["encryption_spec"]) is not None:
        setv(
            to_object,
            ["encryptionSpec"],
            getv(from_object, ["encryption_spec"]),
        )

    if getv(from_object, ["end_time"]) is not None:
        setv(to_object, ["endTime"], getv(from_object, ["end_time"]))

    if getv(from_object, ["error"]) is not None:
        setv(to_object, ["error"], getv(from_object, ["error"]))

    if getv(from_object, ["labels"]) is not None:
        setv(to_object, ["labels"], getv(from_object, ["labels"]))

    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["name"], getv(from_object, ["name"]))

    if getv(from_object, ["satisfies_pzi"]) is not None:
        setv(to_object, ["satisfiesPzi"], getv(from_object, ["satisfies_pzi"]))

    if getv(from_object, ["satisfies_pzs"]) is not None:
        setv(to_object, ["satisfiesPzs"], getv(from_object, ["satisfies_pzs"]))

    if getv(from_object, ["start_time"]) is not None:
        setv(to_object, ["startTime"], getv(from_object, ["start_time"]))

    if getv(from_object, ["state"]) is not None:
        setv(to_object, ["state"], getv(from_object, ["state"]))

    if getv(from_object, ["update_time"]) is not None:
        setv(to_object, ["updateTime"], getv(from_object, ["update_time"]))

    if getv(from_object, ["web_access_uris"]) is not None:
        setv(to_object, ["webAccessUris"], getv(from_object, ["web_access_uris"]))

    return to_object


def _CustomJobParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["custom_job"]) is not None:
        setv(
            parent_object,
            ["customJob"],
            _CustomJob_to_vertex(getv(from_object, ["custom_job"]), to_object),
        )

    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _GetCustomJobParameters_to_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["_url", "name"], getv(from_object, ["name"]))

    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _OptimizeResponse_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    return to_object


def _CustomJobSpec_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["baseOutputDirectory"]) is not None:
        setv(
            to_object,
            ["base_output_directory"],
            getv(from_object, ["baseOutputDirectory"]),
        )

    if getv(from_object, ["enableDashboardAccess"]) is not None:
        setv(
            to_object,
            ["enable_dashboard_access"],
            getv(from_object, ["enableDashboardAccess"]),
        )

    if getv(from_object, ["enableWebAccess"]) is not None:
        setv(
            to_object,
            ["enable_web_access"],
            getv(from_object, ["enableWebAccess"]),
        )

    if getv(from_object, ["experiment"]) is not None:
        setv(to_object, ["experiment"], getv(from_object, ["experiment"]))

    if getv(from_object, ["experimentRun"]) is not None:
        setv(to_object, ["experiment_run"], getv(from_object, ["experimentRun"]))

    if getv(from_object, ["models"]) is not None:
        setv(to_object, ["models"], getv(from_object, ["models"]))

    if getv(from_object, ["network"]) is not None:
        setv(to_object, ["network"], getv(from_object, ["network"]))

    if getv(from_object, ["persistentResourceId"]) is not None:
        setv(
            to_object,
            ["persistent_resource_id"],
            getv(from_object, ["persistentResourceId"]),
        )

    if getv(from_object, ["protectedArtifactLocationId"]) is not None:
        setv(
            to_object,
            ["protected_artifact_location_id"],
            getv(from_object, ["protectedArtifactLocationId"]),
        )

    if getv(from_object, ["pscInterfaceConfig"]) is not None:
        setv(
            to_object,
            ["psc_interface_config"],
            getv(from_object, ["pscInterfaceConfig"]),
        )

    if getv(from_object, ["reservedIpRanges"]) is not None:
        setv(
            to_object,
            ["reserved_ip_ranges"],
            getv(from_object, ["reservedIpRanges"]),
        )

    if getv(from_object, ["scheduling"]) is not None:
        setv(to_object, ["scheduling"], getv(from_object, ["scheduling"]))

    if getv(from_object, ["serviceAccount"]) is not None:
        setv(
            to_object,
            ["service_account"],
            getv(from_object, ["serviceAccount"]),
        )

    if getv(from_object, ["tensorboard"]) is not None:
        setv(to_object, ["tensorboard"], getv(from_object, ["tensorboard"]))

    if getv(from_object, ["workerPoolSpecs"]) is not None:
        setv(
            to_object,
            ["worker_pool_specs"],
            getv(from_object, ["workerPoolSpecs"]),
        )

    return to_object


def _CustomJob_from_vertex(
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(parent_object, ["displayName"]) is not None:
        setv(to_object, ["display_name"], getv(parent_object, ["displayName"]))

    if getv(parent_object, ["jobSpec"]) is not None:
        setv(
            to_object,
            ["job_spec"],
            _CustomJobSpec_from_vertex(getv(parent_object, ["jobSpec"]), to_object),
        )

    if getv(from_object, ["createTime"]) is not None:
        setv(to_object, ["create_time"], getv(from_object, ["createTime"]))

    if getv(from_object, ["encryptionSpec"]) is not None:
        setv(
            to_object,
            ["encryption_spec"],
            getv(from_object, ["encryptionSpec"]),
        )

    if getv(from_object, ["endTime"]) is not None:
        setv(to_object, ["end_time"], getv(from_object, ["endTime"]))

    if getv(from_object, ["error"]) is not None:
        setv(to_object, ["error"], getv(from_object, ["error"]))

    if getv(from_object, ["labels"]) is not None:
        setv(to_object, ["labels"], getv(from_object, ["labels"]))

    if getv(from_object, ["name"]) is not None:
        setv(to_object, ["name"], getv(from_object, ["name"]))

    if getv(from_object, ["satisfiesPzi"]) is not None:
        setv(to_object, ["satisfies_pzi"], getv(from_object, ["satisfiesPzi"]))

    if getv(from_object, ["satisfiesPzs"]) is not None:
        setv(to_object, ["satisfies_pzs"], getv(from_object, ["satisfiesPzs"]))

    if getv(from_object, ["startTime"]) is not None:
        setv(to_object, ["start_time"], getv(from_object, ["startTime"]))

    if getv(from_object, ["state"]) is not None:
        setv(to_object, ["state"], getv(from_object, ["state"]))

    if getv(from_object, ["updateTime"]) is not None:
        setv(to_object, ["update_time"], getv(from_object, ["updateTime"]))

    if getv(from_object, ["webAccessUris"]) is not None:
        setv(to_object, ["web_access_uris"], getv(from_object, ["webAccessUris"]))

    return to_object


class PromptOptimizer(_api_module.BaseModule):
    """Prompt Optimizer"""

    def _optimize_dummy(
        self, *, config: Optional[types.OptimizeConfigOrDict] = None
    ) -> types.OptimizeResponse:
        """Optimize multiple prompts."""

        parameter_model = types._OptimizeRequestParameters(
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _OptimizeRequestParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = ":optimize".format_map(request_url_dict)
            else:
                path = ":optimize"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("post", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _OptimizeResponse_from_vertex(response_dict)

        return_value = types.OptimizeResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _create_custom_job_resource(
        self,
        *,
        custom_job: types.CustomJobOrDict,
        config: Optional[types.BaseConfigOrDict] = None,
    ) -> types.CustomJob:
        """Creates a custom job."""

        parameter_model = types._CustomJobParameters(
            custom_job=custom_job,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _CustomJobParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "customJobs".format_map(request_url_dict)
            else:
                path = "customJobs"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("post", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _CustomJob_from_vertex(response_dict)

        return_value = types.CustomJob._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    def _get_custom_job(
        self, *, name: str, config: Optional[types.BaseConfigOrDict] = None
    ) -> types.CustomJob:
        """Gets a custom job."""

        parameter_model = types._GetCustomJobParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GetCustomJobParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "customJobs/{name}".format_map(request_url_dict)
            else:
                path = "customJobs/{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = self._api_client.request("get", path, request_dict, http_options)

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _CustomJob_from_vertex(response_dict)

        return_value = types.CustomJob._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    """Prompt Optimizer PO-Data."""

    def _wait_for_completion(self, job_name: str) -> None:

        JOB_COMPLETE_STATES = [
            types.JobState.JOB_STATE_SUCCEEDED,
            types.JobState.JOB_STATE_FAILED,
            types.JobState.JOB_STATE_CANCELLED,
            types.JobState.JOB_STATE_PAUSED,
        ]
        JOB_ERROR_STATES = [
            types.JobState.JOB_STATE_FAILED,
            types.JobState.JOB_STATE_CANCELLED,
        ]

        log_wait = 5
        wait_multiplier = 2
        max_wait_time = 60
        previous_time = time.time()

        job = self._get_custom_job(name=job_name)

        while job.state not in JOB_COMPLETE_STATES:
            current_time = time.time()
            if current_time - previous_time >= log_wait:
                logger.info(f"Waiting for job to complete. Current state: {job.state}")
                log_wait = min(log_wait * wait_multiplier, max_wait_time)
                previous_time = current_time
            time.sleep(log_wait)
            job = self._get_custom_job(name=job_name)

        logger.info(f"Job state: {job.state}")

        if job.state in JOB_ERROR_STATES:
            raise RuntimeError(f"Job failed with state: {job.state}")
        else:
            logger.info(f"Job completed with state: {job.state}")

    def optimize(
        self,
        method: str,
        config: types.PromptOptimizerVAPOConfigOrDict,
    ) -> types.CustomJob:
        """Call PO-Data optimizer.

        Args:
          method: The method for optimizing multiple prompts.
          config: The config to use. Config  consists of the following fields: -
            config_path: The gcs path to the config file, e.g.
            gs://bucket/config.json. - service_account: Optional. The service
              account to use for the custom job. Cannot be provided at the same
              time as 'service_account_project_number'. -
            service_account_project_number: Optional. The project number used to
              construct the default service account:
              f"{service_account_project_number}-<EMAIL>"
              Cannot be provided at the same time as 'service_account'. -
            wait_for_completion: Optional. Whether to wait for the job to
              complete. Default is True.
        """

        if method != "vapo":
            raise ValueError("Only vapo methods is currently supported.")

        if isinstance(config, dict):
            config = types.PromptOptimizerVAPOConfig(**config)

        timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
        display_name = f"vapo-optimizer-{timestamp}"
        wait_for_completion = config.wait_for_completion
        if not config.config_path:
            raise ValueError("Config path is required.")
        bucket = "/".join(config.config_path.split("/")[:-1])

        container_uri = "us-docker.pkg.dev/vertex-ai/cair/vaipo:preview_v1_0"

        region = self._api_client.location
        project = self._api_client.project
        container_args = {
            "config": config.config_path,
        }
        args = ["--%s=%s" % (k, v) for k, v in container_args.items()]
        worker_pool_specs = [
            {
                "replica_count": 1,
                "container_spec": {
                    "image_uri": container_uri,
                    "args": args,
                },
                "machine_spec": {
                    "machine_type": "n1-standard-4",
                },
            }
        ]

        if config.service_account:
            if config.service_account_project_number:
                raise ValueError(
                    "Only one of service_account or"
                    " service_account_project_number can be provided."
                )
            service_account = config.service_account
        elif config.project_number:
            service_account = (
                f"{config.service_account_project_number}"
                "-<EMAIL>"
            )
        else:
            raise ValueError(
                "Either service_account or service_account_project_number is"
                " required."
            )

        job_spec = types.CustomJobSpec(
            worker_pool_specs=worker_pool_specs,
            base_output_directory=types.GcsDestination(output_uri_prefix=bucket),
            service_account=service_account,
        )

        custom_job = types.CustomJob(
            display_name=display_name,
            job_spec=job_spec,
        )

        job = self._create_custom_job_resource(
            custom_job=custom_job,
        )

        # Get the job resource name
        job_resource_name = job.name
        if not job_resource_name:
            raise ValueError(f"Error creating job: {job}")
        job_id = job_resource_name.split("/")[-1]
        logger.info("Job created: %s", job.name)

        # Construct the dashboard URL
        dashboard_url = f"https://pantheon.corp.google.com/vertex-ai/locations/{region}/training/{job_id}/cpu?e=********&project={project}"
        logger.info("View the job status at: %s", dashboard_url)

        if wait_for_completion:
            self._wait_for_completion(job_id)
        return job


class AsyncPromptOptimizer(_api_module.BaseModule):
    """Prompt Optimizer"""

    async def _optimize_dummy(
        self, *, config: Optional[types.OptimizeConfigOrDict] = None
    ) -> types.OptimizeResponse:
        """Optimize multiple prompts."""

        parameter_model = types._OptimizeRequestParameters(
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _OptimizeRequestParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = ":optimize".format_map(request_url_dict)
            else:
                path = ":optimize"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "post", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _OptimizeResponse_from_vertex(response_dict)

        return_value = types.OptimizeResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _create_custom_job_resource(
        self,
        *,
        custom_job: types.CustomJobOrDict,
        config: Optional[types.BaseConfigOrDict] = None,
    ) -> types.CustomJob:
        """Creates a custom job."""

        parameter_model = types._CustomJobParameters(
            custom_job=custom_job,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _CustomJobParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "customJobs".format_map(request_url_dict)
            else:
                path = "customJobs"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "post", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _CustomJob_from_vertex(response_dict)

        return_value = types.CustomJob._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value

    async def _get_custom_job(
        self, *, name: str, config: Optional[types.BaseConfigOrDict] = None
    ) -> types.CustomJob:
        """Gets a custom job."""

        parameter_model = types._GetCustomJobParameters(
            name=name,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _GetCustomJobParameters_to_vertex(parameter_model)
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = "customJobs/{name}".format_map(request_url_dict)
            else:
                path = "customJobs/{name}"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response = await self._api_client.async_request(
            "get", path, request_dict, http_options
        )

        response_dict = "" if not response.body else json.loads(response.body)

        if self._api_client.vertexai:
            response_dict = _CustomJob_from_vertex(response_dict)

        return_value = types.CustomJob._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )

        self._api_client._verify_response(return_value)
        return return_value
