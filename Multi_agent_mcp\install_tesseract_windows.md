# Installing Tesseract OCR on Windows

## The Issue
The "Connection closed" error in our A2A OCR system is likely caused by missing Tesseract OCR installation. The MCP OCR tool requires Tesseract to be installed and available in the system PATH.

## Solution: Install Tesseract OCR

### Option 1: Download and Install Manually
1. Download Tesseract installer from: https://github.com/UB-Mannheim/tesseract/wiki
2. Choose the latest version (e.g., `tesseract-ocr-w64-setup-5.3.3.20231005.exe`)
3. Run the installer and follow the setup wizard
4. **Important**: During installation, make sure to check "Add Tesseract to PATH"
5. Restart your command prompt/terminal after installation

### Option 2: Using Chocolatey (if available)
```cmd
choco install tesseract
```

### Option 3: Using Scoop (if available)
```cmd
scoop install tesseract
```

## Verify Installation
After installation, verify Tesseract is working:
```cmd
tesseract --version
```

You should see output like:
```
tesseract 5.3.3
 leptonica-1.83.1
  libgif 5.2.1 : libjpeg 8d (libjpeg-turbo 2.1.4) : libpng 1.6.39 : libtiff 4.5.1 : zlib 1.2.13 : libwebp 1.3.2 : libopenjp2 2.5.0
```

## Test OCR Functionality
Create a test to verify OCR is working:
```cmd
echo "Hello World" > test.txt
tesseract test.txt output
type output.txt
```

## Next Steps
After installing Tesseract:
1. Restart your terminal/command prompt
2. Test the MCP OCR tool: `python -m mcp_ocr`
3. Run the A2A OCR client again to test the connection
