Metadata-Version: 2.4
Name: mcp-ocr
Version: 0.1.2
Summary: MCP server for OCR functionality using Tesseract
Project-URL: Homepage, https://github.com/yourusername/mcp-ocr
Project-URL: Documentation, https://github.com/yourusername/mcp-ocr#readme
Project-URL: Issues, https://github.com/yourusername/mcp-ocr/issues
Author-email: <PERSON><PERSON> <<EMAIL>>
License: MIT
License-File: LICENSE
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.11
Requires-Dist: httpx>=0.24.0
Requires-Dist: mcp[cli]>=1.2.0
Requires-Dist: numpy>=1.24.0
Requires-Dist: opencv-python>=4.8.0
Requires-Dist: pillow>=10.0.0
Requires-Dist: pytesseract>=0.3.10
Provides-Extra: test
Requires-Dist: pytest-asyncio>=0.21.0; extra == 'test'
Requires-Dist: pytest-cov>=4.0.0; extra == 'test'
Requires-Dist: pytest>=7.0.0; extra == 'test'
Description-Content-Type: text/markdown

# MCP OCR Server

A production-grade OCR server built using MCP (Model Context Protocol) that provides OCR capabilities through a simple interface.

## Features

- Extract text from images using Tesseract OCR
- Support for multiple input types:
  - Local image files
  - Image URLs
  - Raw image bytes
- Automatic Tesseract installation
- Support for multiple languages
- Production-ready error handling

## Installation

```bash
# Using pip
pip install mcp-ocr

# Using uv
uv pip install mcp-ocr
```

Tesseract will be installed automatically on supported platforms:
- macOS (via Homebrew)
- Linux (via apt, dnf, or pacman)
- Windows (manual installation instructions provided)

## Usage

### As an MCP Server

1. Start the server:
```bash
python -m mcp_ocr
```

2. Configure Claude for Desktop:
Add to `~/Library/Application Support/Claude/claude_desktop_config.json`:
```json
{
    "mcpServers": {
        "ocr": {
            "command": "python",
            "args": ["-m", "mcp_ocr"]
        }
    }
}
```

### Available Tools

#### perform_ocr
Extract text from images:
```python
# From file
perform_ocr("/path/to/image.jpg")

# From URL
perform_ocr("https://example.com/image.jpg")

# From bytes
perform_ocr(image_bytes)
```

#### get_supported_languages
List available OCR languages:
```python
get_supported_languages()
```

## Development

1. Clone the repository:
```bash
git clone https://github.com/rjn32s/mcp-ocr.git
cd mcp-ocr
```

2. Set up development environment:
```bash
uv venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
uv pip install -e .
```

3. Run tests:
```bash
pytest
```


## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Security

- Never commit API tokens or sensitive credentials
- Use environment variables or secure credential storage
- Follow GitHub's security best practices

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [Tesseract OCR](https://github.com/tesseract-ocr/tesseract)
- [Model Context Protocol](https://modelcontextprotocol.io)
