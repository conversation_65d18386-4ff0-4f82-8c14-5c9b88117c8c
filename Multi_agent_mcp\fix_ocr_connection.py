#!/usr/bin/env python3
"""
Quick Fix Script for OCR Connection Issues

This script applies common fixes for the "Connection closed" error.
"""

import os
import sys
import subprocess
import json
from pathlib import Path


def install_tesseract_windows():
    """Install Tesseract on Windows using available package managers."""
    print("🔧 Installing Tesseract OCR for Windows...")
    
    # Try winget first
    try:
        result = subprocess.run(
            ["winget", "install", "UB-Mannheim.TesseractOCR"],
            capture_output=True,
            text=True,
            check=False
        )
        if result.returncode == 0:
            print("✅ Tesseract installed via winget")
            return True
    except FileNotFoundError:
        pass
    
    # Try chocolatey
    try:
        result = subprocess.run(
            ["choco", "install", "tesseract", "-y"],
            capture_output=True,
            text=True,
            check=False
        )
        if result.returncode == 0:
            print("✅ Tesseract installed via chocolatey")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Could not install Tesseract automatically")
    print("Please install manually from: https://github.com/UB-Mannheim/tesseract/wiki")
    return False


def install_missing_packages():
    """Install missing Python packages."""
    print("🔧 Installing missing Python packages...")
    
    packages = [
        "pytesseract",
        "Pillow",
        "httpx",
        "mcp-ocr"
    ]
    
    for package in packages:
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", package],
                capture_output=True,
                text=True,
                check=False
            )
            if result.returncode == 0:
                print(f"✅ Installed {package}")
            else:
                print(f"⚠️  Failed to install {package}: {result.stderr}")
        except Exception as e:
            print(f"❌ Error installing {package}: {e}")


def create_fixed_agent_config():
    """Create a fixed agent configuration."""
    print("🔧 Creating fixed agent configuration...")
    
    config_content = '''import os
import logging
from typing import Dict, Any, List
from google.adk.agents.llm_agent import Agent
from google.adk.tools.tool import Tool
from PIL import Image
import pytesseract
import base64
import io

logger = logging.getLogger(__name__)

class FixedOCRTool(Tool):
    """Fixed OCR tool that handles connection issues."""
    
    def __init__(self):
        super().__init__(
            name="perform_ocr",
            description="Extract text from images using OCR with error handling",
            parameters={
                "type": "object",
                "properties": {
                    "images": {
                        "type": "array",
                        "description": "Array of image data",
                        "items": {"type": "object"}
                    }
                },
                "required": ["images"]
            }
        )
    
    def execute(self, images: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Execute OCR with proper error handling."""
        try:
            # Check if Tesseract is available
            try:
                pytesseract.get_tesseract_version()
            except Exception as e:
                return {
                    "status": "error",
                    "error": f"Tesseract not available: {e}",
                    "suggestion": "Please install Tesseract OCR"
                }
            
            results = []
            for i, image_info in enumerate(images):
                try:
                    # Process image
                    if "data" in image_info:
                        image_data = base64.b64decode(image_info["data"])
                        image = Image.open(io.BytesIO(image_data))
                    else:
                        results.append({
                            "index": i,
                            "error": "No image data provided",
                            "status": "error"
                        })
                        continue
                    
                    # Perform OCR
                    text = pytesseract.image_to_string(image)
                    
                    results.append({
                        "index": i,
                        "name": image_info.get("name", f"image_{i}"),
                        "text": text.strip(),
                        "status": "success"
                    })
                    
                except Exception as e:
                    results.append({
                        "index": i,
                        "error": str(e),
                        "status": "error"
                    })
            
            return {
                "status": "success",
                "results": results,
                "total_images": len(images)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

def create_fixed_ocr_agent() -> Agent:
    """Create OCR agent with fixed configuration."""
    return Agent(
        name="fixed_ocr_agent",
        model="gemini-2.0-flash",
        description="Fixed OCR agent with proper error handling",
        instruction="You are an OCR agent. Extract text from images and provide detailed analysis.",
        tools=[FixedOCRTool()],
    )
'''
    
    # Write the fixed agent configuration
    fixed_agent_path = Path("ocr_agent/fixed_agent.py")
    fixed_agent_path.parent.mkdir(exist_ok=True)
    
    with open(fixed_agent_path, 'w') as f:
        f.write(config_content)
    
    print(f"✅ Created fixed agent configuration: {fixed_agent_path}")


def update_main_server():
    """Update the main server to use the fixed agent."""
    print("🔧 Updating main server configuration...")
    
    main_file = Path("ocr_agent/_main_.py")
    if not main_file.exists():
        print(f"❌ Main server file not found: {main_file}")
        return
    
    # Read current content
    with open(main_file, 'r') as f:
        content = f.read()
    
    # Add import for fixed agent
    if "from ocr_agent.fixed_agent import create_fixed_ocr_agent" not in content:
        # Find the import section and add the fixed agent import
        import_section = "from google.adk.sessions import InMemorySessionService"
        if import_section in content:
            content = content.replace(
                import_section,
                import_section + "\nfrom ocr_agent.fixed_agent import create_fixed_ocr_agent"
            )
        
        # Update the agent creation to use fixed agent as fallback
        if "create_ocr_agent" in content and "create_fixed_ocr_agent" not in content:
            content = content.replace(
                "agent = create_ocr_agent()",
                """try:
    agent = create_ocr_agent()
    logger.info("✅ Using standard OCR agent")
except Exception as e:
    logger.warning(f"⚠️  Standard agent failed: {e}")
    logger.info("🔄 Using fixed OCR agent")
    agent = create_fixed_ocr_agent()"""
            )
        
        # Write updated content
        with open(main_file, 'w') as f:
            f.write(content)
        
        print("✅ Updated main server configuration")


def test_fix():
    """Test if the fix worked."""
    print("🧪 Testing the fix...")
    
    try:
        # Test Tesseract
        import pytesseract
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract working: {version}")
        
        # Test direct OCR
        from PIL import Image, ImageDraw
        img = Image.new('RGB', (200, 100), color='white')
        draw = ImageDraw.Draw(img)
        draw.text((10, 30), "TEST", fill='black')
        
        text = pytesseract.image_to_string(img)
        print(f"✅ OCR test result: '{text.strip()}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Fix test failed: {e}")
        return False


def main():
    """Apply all fixes."""
    print("🔧 OCR Connection Fix Tool")
    print("=" * 40)
    
    # Step 1: Install missing packages
    install_missing_packages()
    
    # Step 2: Install Tesseract (Windows)
    if os.name == 'nt':  # Windows
        install_tesseract_windows()
    
    # Step 3: Create fixed agent configuration
    create_fixed_agent_config()
    
    # Step 4: Update main server
    update_main_server()
    
    # Step 5: Test the fix
    if test_fix():
        print("\n🎉 Fix applied successfully!")
        print("Please restart the OCR agent server and try again.")
    else:
        print("\n⚠️  Fix may not be complete. Please check the diagnostic output.")


if __name__ == "__main__":
    main()
