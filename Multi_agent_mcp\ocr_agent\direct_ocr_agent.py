import json
import base64
import io
from typing import List, Dict, Any
from PIL import Image
import pytesseract
import logging

from google.adk.agents.llm_agent import Agent
from google.adk.tools.tool import Tool

from ocr_agent.prompt import OCR_PROMPT

logger = logging.getLogger(__name__)


class DirectOCRTool(Tool):
    """Direct OCR tool that doesn't rely on MCP."""
    
    def __init__(self):
        super().__init__(
            name="perform_ocr",
            description="Extract text from images using OCR",
            parameters={
                "type": "object",
                "properties": {
                    "images": {
                        "type": "array",
                        "description": "Array of image data (base64 encoded) or file paths",
                        "items": {
                            "type": "object",
                            "properties": {
                                "data": {"type": "string", "description": "Base64 encoded image data"},
                                "path": {"type": "string", "description": "File path to image"},
                                "name": {"type": "string", "description": "Image name"},
                                "description": {"type": "string", "description": "Image description"}
                            }
                        }
                    }
                },
                "required": ["images"]
            }
        )
    
    def execute(self, images: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Execute OCR on the provided images."""
        try:
            results = []
            
            for i, image_info in enumerate(images):
                try:
                    # Get image data
                    if "data" in image_info:
                        # Base64 encoded image
                        image_data = base64.b64decode(image_info["data"])
                        image = Image.open(io.BytesIO(image_data))
                    elif "path" in image_info:
                        # File path
                        image = Image.open(image_info["path"])
                    else:
                        results.append({
                            "index": i,
                            "name": image_info.get("name", f"image_{i}"),
                            "error": "No image data or path provided"
                        })
                        continue
                    
                    # Perform OCR
                    text = pytesseract.image_to_string(image)
                    
                    # Get additional info
                    try:
                        data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
                        confidence = sum(int(conf) for conf in data['conf'] if int(conf) > 0) / len([conf for conf in data['conf'] if int(conf) > 0])
                    except:
                        confidence = None
                    
                    result = {
                        "index": i,
                        "name": image_info.get("name", f"image_{i}"),
                        "description": image_info.get("description", ""),
                        "text": text.strip(),
                        "confidence": confidence,
                        "word_count": len(text.split()) if text.strip() else 0,
                        "status": "success"
                    }
                    
                    results.append(result)
                    logger.info(f"Successfully processed image {i}: {len(text)} characters extracted")
                    
                except Exception as e:
                    logger.error(f"Error processing image {i}: {e}")
                    results.append({
                        "index": i,
                        "name": image_info.get("name", f"image_{i}"),
                        "error": str(e),
                        "status": "error"
                    })
            
            return {
                "status": "success",
                "results": results,
                "total_images": len(images),
                "successful_images": len([r for r in results if r.get("status") == "success"])
            }
            
        except Exception as e:
            logger.error(f"OCR processing failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "results": []
            }


def create_direct_ocr_agent() -> Agent:
    """Create an OCR agent with direct OCR implementation."""
    
    # Check if Tesseract is available
    try:
        pytesseract.get_tesseract_version()
        logger.info("Tesseract OCR is available")
    except Exception as e:
        logger.warning(f"Tesseract OCR may not be properly installed: {e}")
    
    return Agent(
        name="direct_ocr_agent",
        model="gemini-2.0-flash",
        description="Agent that processes images and extracts content using direct OCR capabilities.",
        instruction=OCR_PROMPT,
        tools=[DirectOCRTool()],
    )


# Create the agent instance
direct_ocr_agent = create_direct_ocr_agent()
