# Quick Start Guide - A2A OCR System

This guide will help you quickly start and test the complete A2A OCR system.

## Prerequisites

1. **Python 3.8+** installed
2. **Required dependencies** installed
3. **OCR agent server** ready to run

## Step-by-Step Setup

### 1. Install Dependencies

First, install the required packages:

```bash
# Navigate to the Multi_agent_mcp directory
cd Multi_agent_mcp

# Install FastAPI server dependencies
pip install -r requirements_fastapi_server.txt

# Install OCR agent dependencies (if not already installed)
pip install -r requirements.txt
```

### 2. Start the OCR Agent Server (Layer 2)

The OCR agent server must be running first. You have several options:

#### Option A: Using the startup script (Recommended)
```bash
# From Multi_agent_mcp directory
python start_ocr_server.py
```

#### Option B: Direct execution
```bash
# From Multi_agent_mcp directory
cd ocr_agent
python _main_.py --host localhost --port 8003
cd ..
```

#### Option C: Using the complete system starter
```bash
# From Multi_agent_mcp directory
python start_complete_system.py
```

**Expected Output:**
```
🚀 Starting OCR agent server on localhost:8003
✅ Server is healthy and responding at http://localhost:8003
🌐 Agent card: http://localhost:8003/.well-known/agent.json
🔗 A2A endpoint: http://localhost:8003/a2a/ocr_agent
```

**Keep this terminal open** - the OCR server needs to stay running.

### 3. Start the FastAPI HTTP Server (Layer 1)

Open a **new terminal** and navigate to the Multi_agent_mcp directory, then start the FastAPI server:

```bash
# Navigate to Multi_agent_mcp directory
cd Multi_agent_mcp

# Basic startup
python fastapi_ocr_server.py

# Or with custom configuration
python fastapi_ocr_server.py --host 0.0.0.0 --port 8005 --ocr-server http://localhost:8003
```

**Expected Output:**
```
INFO:     Started server process [12345]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8005 (Press CTRL+C to quit)
```

### 4. Verify Both Servers Are Running

#### Check OCR Agent Server:
```bash
curl http://localhost:8003/.well-known/agent.json
```

#### Check FastAPI Server:
```bash
curl http://localhost:8005/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01 12:00:00 UTC",
  "ocr_server_url": "http://localhost:8003",
  "supported_formats": [".jpg", ".jpeg", ".png", ".pdf", ".tiff", ".tif", ".bmp", ".gif", ".webp", ".svg"],
  "max_file_size": 10485760
}
```

## Testing the System

### Option 1: Using the A2A Client Directly

Test the A2A client directly (bypasses the HTTP layer):

```bash
# From Multi_agent_mcp directory
# Process a single image
python a2a_ocr_client.py -i path/to/your/image.jpg

# Process multiple images
python a2a_ocr_client.py -i image1.jpg -i image2.png

# Process all images in a directory
python a2a_ocr_client.py -d ./test_images --recursive

# Save results to file
python a2a_ocr_client.py -i image.jpg -o results.json
```

### Option 2: Using the HTTP API with curl

Test the complete two-layer system:

```bash
# Upload a single file
curl -X POST "http://localhost:8005/upload" \
  -F "files=@path/to/your/image.jpg" \
  -F "user_id=test_user" \
  -F "description=Test OCR processing"

# Upload multiple files
curl -X POST "http://localhost:8005/upload" \
  -F "files=@image1.jpg" \
  -F "files=@image2.png" \
  -F "user_id=test_user" \
  -F "description=Batch OCR test"
```

### Option 3: Using Postman

1. **Import the collection**: Import `postman_collection.json` into Postman
2. **Set variables**: 
   - `base_url`: `http://localhost:8005`
   - `ocr_server_url`: `http://localhost:8003`
3. **Run tests**: Execute the requests in the collection

### Option 4: Generate and Use Test Data

Generate test images for testing:

```bash
# From Multi_agent_mcp directory
python test_ocr_system.py --generate-test-data
```

This creates test images in the `test_data/` directory that you can use for testing.

## API Documentation

Once the FastAPI server is running, visit:

- **Swagger UI**: http://localhost:8005/docs
- **ReDoc**: http://localhost:8005/redoc

## Common Issues and Solutions

### Issue: "Connection refused" when testing
**Solution**: Make sure both servers are running:
1. OCR agent server on port 8003
2. FastAPI server on port 8005

### Issue: "OCR agent server not responding"
**Solution**: 
1. Check if the OCR server is running: `curl http://localhost:8003/.well-known/agent.json`
2. Restart the OCR server if needed
3. Check for port conflicts

### Issue: "File format not supported"
**Solution**: Ensure you're using supported formats:
- Images: JPG, JPEG, PNG, BMP, GIF, WEBP, TIFF, TIF
- Documents: PDF
- Vector: SVG

### Issue: "File too large"
**Solution**: Files must be under 10MB. Resize or compress large files.

### Issue: Import errors
**Solution**: Make sure all dependencies are installed:
```bash
# From Multi_agent_mcp directory
pip install -r requirements_fastapi_server.txt
pip install -r requirements.txt
```

## System Architecture

```
┌─────────────────┐    HTTP POST     ┌──────────────────┐    A2A Protocol    ┌─────────────────┐
│   HTTP Client   │ ──────────────► │  FastAPI Server  │ ─────────────────► │  A2A OCR Client │
│   (Postman)     │  multipart/     │   (Layer 1)      │   JSON-RPC over   │   (Layer 2)     │
│                 │  form-data      │   Port 8005      │   HTTP             │                 │
└─────────────────┘                 └──────────────────┘                    └─────────────────┘
                                                                                      │
                                                                                      │ A2A Protocol
                                                                                      ▼
                                                                            ┌─────────────────┐
                                                                            │  OCR Agent      │
                                                                            │  Server         │
                                                                            │  Port 8003      │
                                                                            └─────────────────┘
```

## Next Steps

1. **Test with your own images**: Upload your documents/images via Postman or curl
2. **Integrate with your application**: Use the HTTP API endpoints in your application
3. **Monitor performance**: Check the logs and response times
4. **Scale if needed**: Consider load balancing for production use

## Stopping the Servers

To stop the servers:
1. **FastAPI Server**: Press `Ctrl+C` in the FastAPI terminal
2. **OCR Agent Server**: Press `Ctrl+C` in the OCR server terminal

## Support

If you encounter issues:
1. Check both server logs for error messages
2. Verify all dependencies are installed
3. Ensure no other services are using ports 8003 or 8005
4. Run the health checks to verify system status
