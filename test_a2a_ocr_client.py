#!/usr/bin/env python3
"""
Test script for the A2A OCR Client

This script tests the A2A OCR client functionality without requiring actual image files.
It demonstrates the client's ability to connect to the server and handle various scenarios.
"""

import asyncio
import json
import logging
import sys
from pathlib import Path

from a2a_ocr_client import A2AOCRClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_connection():
    """Test basic connection to the OCR agent server."""
    print("\n" + "="*60)
    print("TEST 1: Connection Test")
    print("="*60)
    
    try:
        async with A2AOCRClient() as client:
            print("✅ Successfully connected to OCR agent server")
            return True
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False


async def test_image_formatting():
    """Test image formatting functionality."""
    print("\n" + "="*60)
    print("TEST 2: Image Formatting Test")
    print("="*60)
    
    client = A2AOCRClient()
    
    # Test different input formats
    test_cases = [
        # Simple strings
        ["image1.jpg", "image2.png"],
        
        # Objects with path
        [{"path": "doc.pdf", "name": "Document", "description": "Test doc"}],
        
        # Objects with file key
        [{"file": "receipt.jpg", "name": "Receipt"}],
        
        # Mixed formats
        [
            "simple.jpg",
            {"path": "complex.pdf", "name": "Complex Doc", "description": "Detailed doc"},
            {"file": "another.png"}
        ]
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            formatted = client._format_images(test_case)
            print(f"✅ Test case {i}: {len(formatted)} images formatted correctly")
            print(f"   Input: {test_case}")
            print(f"   Output: {json.dumps(formatted, indent=2)}")
        except Exception as e:
            print(f"❌ Test case {i} failed: {e}")
    
    return True


async def test_mock_processing():
    """Test processing with mock data (no actual server call)."""
    print("\n" + "="*60)
    print("TEST 3: Mock Processing Test")
    print("="*60)
    
    # Test the image formatting without actually sending to server
    mock_images = [
        {
            "path": "test_invoice.pdf",
            "name": "Test Invoice",
            "description": "Mock invoice for testing"
        },
        "test_receipt.jpg",
        {
            "file": "test_document.png",
            "name": "Test Document",
            "description": "Mock document for testing"
        }
    ]
    
    client = A2AOCRClient()
    formatted_images = client._format_images(mock_images)
    
    print(f"✅ Formatted {len(formatted_images)} mock images:")
    for i, img in enumerate(formatted_images, 1):
        print(f"   Image {i}:")
        print(f"     Path: {img['path']}")
        print(f"     Name: {img['name']}")
        print(f"     Description: {img['description']}")
    
    # Test JSON serialization (what would be sent to server)
    json_payload = json.dumps(formatted_images)
    print(f"\n✅ JSON payload size: {len(json_payload)} characters")
    print("✅ JSON payload is valid and ready for transmission")
    
    return True


async def test_error_handling():
    """Test error handling scenarios."""
    print("\n" + "="*60)
    print("TEST 4: Error Handling Test")
    print("="*60)
    
    client = A2AOCRClient()
    
    # Test empty images list
    try:
        await client.process_images([])
        print("❌ Should have raised ValueError for empty images list")
    except ValueError as e:
        print(f"✅ Correctly handled empty images list: {e}")
    except Exception as e:
        print(f"❌ Unexpected error for empty images: {e}")
    
    # Test client not connected
    try:
        await client.process_images(["test.jpg"])
        print("❌ Should have raised ValueError for unconnected client")
    except ValueError as e:
        print(f"✅ Correctly handled unconnected client: {e}")
    except Exception as e:
        print(f"❌ Unexpected error for unconnected client: {e}")
    
    return True


async def test_server_integration():
    """Test actual server integration if server is available."""
    print("\n" + "="*60)
    print("TEST 5: Server Integration Test")
    print("="*60)
    
    try:
        async with A2AOCRClient() as client:
            # Test with mock image data (server should handle non-existent files gracefully)
            mock_images = [
                {
                    "path": "mock_test_image.jpg",
                    "name": "Mock Test Image",
                    "description": "This is a mock image for testing the A2A client integration"
                }
            ]
            
            print("Sending mock image data to server...")
            result = await client.process_images(mock_images)
            
            print("✅ Server responded successfully!")
            print("Response structure:")
            print(json.dumps(result, indent=2))
            
            # Validate response structure
            if isinstance(result, dict):
                if 'status' in result:
                    print(f"✅ Response has status: {result['status']}")
                if 'results' in result or 'content' in result:
                    print("✅ Response has content/results")
                else:
                    print("⚠️  Response missing expected content")
            else:
                print("⚠️  Response is not a dictionary")
            
            return True
            
    except Exception as e:
        print(f"❌ Server integration test failed: {e}")
        print("This is expected if the OCR agent server is not running")
        return False


async def run_all_tests():
    """Run all tests and provide summary."""
    print("A2A OCR Client Test Suite")
    print("=" * 60)
    
    tests = [
        ("Connection Test", test_connection),
        ("Image Formatting Test", test_image_formatting),
        ("Mock Processing Test", test_mock_processing),
        ("Error Handling Test", test_error_handling),
        ("Server Integration Test", test_server_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    elif passed > 0:
        print("⚠️  Some tests passed, some failed")
    else:
        print("💥 All tests failed")
    
    return passed == total


def main():
    """Main function to run tests."""
    print("Starting A2A OCR Client tests...")
    
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nTests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error running tests: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
