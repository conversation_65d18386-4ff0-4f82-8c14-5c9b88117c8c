../../Scripts/mcp-ocr.exe,sha256=WdFtjKEgOFP30do6WZpY9RtLBx5ZT_Db0YbX6kp2cjQ,108416
mcp_ocr-0.1.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
mcp_ocr-0.1.2.dist-info/METADATA,sha256=ZhPA-sH45-EZuPuJKNHpyomF8TgzdZ2BXGiIbsCq-5c,3519
mcp_ocr-0.1.2.dist-info/RECORD,,
mcp_ocr-0.1.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mcp_ocr-0.1.2.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
mcp_ocr-0.1.2.dist-info/entry_points.txt,sha256=hNjDWaUFVvwUkbGdkNfccIhBKQ3bJiIAXOgELIUYIag,50
mcp_ocr-0.1.2.dist-info/licenses/LICENSE,sha256=Ow7YG2wLe3FxX8EpPcGYYp7P9jj-mPIaPA6RFXmZMhQ,1069
mcp_ocr/__init__.py,sha256=kdVlkcc9mgtKInRy58E55DAM403g3MxOgzb8zUPNaV8,234
mcp_ocr/__main__.py,sha256=E2LIGRLIoFTfceHKLqEuQIUceX1HlwABdiBWvLlJA5A,419
mcp_ocr/__pycache__/__init__.cpython-313.pyc,,
mcp_ocr/__pycache__/__main__.cpython-313.pyc,,
mcp_ocr/__pycache__/install_tesseract.cpython-313.pyc,,
mcp_ocr/__pycache__/server.cpython-313.pyc,,
mcp_ocr/install_tesseract.py,sha256=vqJr7Xma3oS6sks5J8vXrwTAAFJA0VPWO-vNM0-IjXk,2707
mcp_ocr/server.py,sha256=cJIdIKekm3H9TqZNQng-Ot4sFlBP_McgwQNeSPi2C6A,6161
