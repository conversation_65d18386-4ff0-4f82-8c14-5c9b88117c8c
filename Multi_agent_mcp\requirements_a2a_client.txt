# A2A OCR Client Requirements
# Install with: pip install -r requirements_a2a_client.txt

# Core A2A dependencies
httpx>=0.25.0
httpx-sse>=0.4.0
pydantic>=2.0.0
click>=8.0.0

# The python-a2a library (assuming it's available in the local environment)
# If the a2a package is not installed, you may need to install it from the local source:
# pip install -e ./src  # or wherever the a2a package source is located

# Optional: For enhanced logging and debugging
rich>=13.0.0

# Development dependencies (optional)
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
mypy>=1.0.0
