#!/usr/bin/env python3
"""
Example usage of the A2A OCR Client

This script demonstrates different ways to use the A2A OCR client to process images.
"""

import asyncio
import json
import logging
from pathlib import Path

from a2a_ocr_client import A2AOCRClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def example_basic_usage():
    """Basic example: Process a list of image files."""
    print("\n" + "="*60)
    print("EXAMPLE 1: Basic Image Processing")
    print("="*60)
    
    # Example image paths (replace with actual image paths)
    image_paths = [
        "sample_images/document1.jpg",
        "sample_images/receipt.png",
        "sample_images/screenshot.png"
    ]
    
    async with A2AOCRClient() as client:
        try:
            result = await client.process_images(image_paths)
            print("OCR Results:")
            print(json.dumps(result, indent=2))
        except Exception as e:
            print(f"Error: {e}")


async def example_with_metadata():
    """Example with image metadata and descriptions."""
    print("\n" + "="*60)
    print("EXAMPLE 2: Images with Metadata")
    print("="*60)
    
    # Images with detailed metadata
    images_with_metadata = [
        {
            "path": "sample_images/invoice.pdf",
            "name": "Monthly Invoice",
            "description": "Company invoice for March 2024"
        },
        {
            "path": "sample_images/business_card.jpg",
            "name": "Business Card",
            "description": "Contact information card from networking event"
        },
        {
            "path": "sample_images/handwritten_note.png",
            "name": "Meeting Notes",
            "description": "Handwritten notes from client meeting"
        }
    ]
    
    async with A2AOCRClient() as client:
        try:
            result = await client.process_images(images_with_metadata)
            print("OCR Results with Metadata:")
            print(json.dumps(result, indent=2))
        except Exception as e:
            print(f"Error: {e}")


async def example_mixed_formats():
    """Example with mixed image formats (strings and objects)."""
    print("\n" + "="*60)
    print("EXAMPLE 3: Mixed Image Formats")
    print("="*60)
    
    # Mix of simple paths and detailed objects
    mixed_images = [
        "sample_images/simple_document.jpg",  # Simple string path
        {
            "path": "sample_images/complex_form.pdf",
            "name": "Tax Form",
            "description": "2024 tax form with multiple sections"
        },
        "sample_images/another_doc.png",  # Another simple path
        {
            "file": "sample_images/receipt_scan.jpg",  # Using 'file' key instead of 'path'
            "name": "Grocery Receipt",
            "description": "Weekly grocery shopping receipt"
        }
    ]
    
    async with A2AOCRClient() as client:
        try:
            result = await client.process_images(mixed_images)
            print("OCR Results (Mixed Formats):")
            print(json.dumps(result, indent=2))
        except Exception as e:
            print(f"Error: {e}")


async def example_with_context():
    """Example using context ID for conversation continuity."""
    print("\n" + "="*60)
    print("EXAMPLE 4: Using Context ID")
    print("="*60)
    
    context_id = "ocr_session_2024_001"
    
    # First batch of images
    batch1 = ["sample_images/page1.jpg", "sample_images/page2.jpg"]
    
    # Second batch of images (continuing the same context)
    batch2 = ["sample_images/page3.jpg", "sample_images/page4.jpg"]
    
    async with A2AOCRClient() as client:
        try:
            # Process first batch
            print("Processing first batch...")
            result1 = await client.process_images(batch1, context_id=context_id)
            print("First batch results:")
            print(json.dumps(result1, indent=2))
            
            print("\nProcessing second batch with same context...")
            result2 = await client.process_images(batch2, context_id=context_id)
            print("Second batch results:")
            print(json.dumps(result2, indent=2))
            
        except Exception as e:
            print(f"Error: {e}")


async def example_custom_server():
    """Example connecting to a custom server URL."""
    print("\n" + "="*60)
    print("EXAMPLE 5: Custom Server URL")
    print("="*60)
    
    custom_server_url = "http://localhost:8003"  # Change as needed
    images = ["sample_images/test_image.jpg"]
    
    async with A2AOCRClient(server_url=custom_server_url, timeout=120) as client:
        try:
            result = await client.process_images(images)
            print(f"Results from {custom_server_url}:")
            print(json.dumps(result, indent=2))
        except Exception as e:
            print(f"Error connecting to {custom_server_url}: {e}")


async def example_error_handling():
    """Example demonstrating error handling."""
    print("\n" + "="*60)
    print("EXAMPLE 6: Error Handling")
    print("="*60)
    
    # Test with non-existent images
    invalid_images = [
        "non_existent_image.jpg",
        {
            "path": "another_missing_file.png",
            "name": "Missing File",
            "description": "This file doesn't exist"
        }
    ]
    
    async with A2AOCRClient() as client:
        try:
            result = await client.process_images(invalid_images)
            print("Unexpected success with invalid images:")
            print(json.dumps(result, indent=2))
        except Exception as e:
            print(f"Expected error occurred: {e}")
            print("This demonstrates proper error handling.")


def create_sample_images_info():
    """Create sample image information for testing."""
    print("\n" + "="*60)
    print("SAMPLE IMAGES SETUP")
    print("="*60)
    print("To run these examples, you'll need sample images in a 'sample_images' directory.")
    print("Create the following structure:")
    print("""
sample_images/
├── document1.jpg
├── receipt.png
├── screenshot.png
├── invoice.pdf
├── business_card.jpg
├── handwritten_note.png
├── simple_document.jpg
├── complex_form.pdf
├── another_doc.png
├── receipt_scan.jpg
├── page1.jpg
├── page2.jpg
├── page3.jpg
├── page4.jpg
└── test_image.jpg
    """)
    print("Replace these with actual image files you want to process.")


async def main():
    """Run all examples."""
    print("A2A OCR Client Examples")
    print("=" * 60)
    
    # Show sample images setup info
    create_sample_images_info()
    
    # Check if sample images directory exists
    sample_dir = Path("sample_images")
    if not sample_dir.exists():
        print(f"\nWarning: {sample_dir} directory not found.")
        print("Creating sample directory structure...")
        sample_dir.mkdir(exist_ok=True)
        print("Please add your image files to the sample_images directory and run again.")
        return
    
    # Run examples (comment out examples you don't want to run)
    try:
        await example_basic_usage()
        await example_with_metadata()
        await example_mixed_formats()
        await example_with_context()
        await example_custom_server()
        await example_error_handling()
    except KeyboardInterrupt:
        print("\nExamples interrupted by user.")
    except Exception as e:
        print(f"\nUnexpected error in examples: {e}")


if __name__ == "__main__":
    asyncio.run(main())
