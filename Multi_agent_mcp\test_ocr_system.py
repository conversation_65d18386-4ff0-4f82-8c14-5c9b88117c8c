#!/usr/bin/env python3
"""
Test Suite for A2A OCR System

Comprehensive testing utilities for the A2A OCR client system.
Tests both the A2A client and FastAPI server components.

Features:
- Unit tests for file validation and processing
- Integration tests for A2A client
- API endpoint tests for FastAPI server
- Performance and load testing
- Mock data generation for testing

Usage:
    python test_ocr_system.py --run-all
    python test_ocr_system.py --test-client
    python test_ocr_system.py --test-server
    python test_ocr_system.py --generate-test-data
"""

import asyncio
import json
import logging
import tempfile
import time
from pathlib import Path
from typing import Any, Dict, List, Optional
import unittest
from unittest.mock import AsyncMock, MagicMock, patch

import httpx
import pytest
from PIL import Image, ImageDraw, ImageFont

# Import our modules
from a2a_ocr_client import A2AOCRClient
from file_utils import FileProcessor, FileValidationError, SUPPORTED_IMAGE_FORMATS
from fastapi_ocr_server import create_app

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestDataGenerator:
    """Generate test data for OCR system testing."""
    
    def __init__(self, output_dir: str = "test_data"):
        """Initialize test data generator."""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
    def create_test_image(self, 
                         text: str = "Test OCR Text", 
                         filename: str = "test_image.png",
                         size: tuple = (400, 200),
                         format: str = "PNG") -> Path:
        """
        Create a test image with text for OCR testing.
        
        Args:
            text: Text to render in the image
            filename: Output filename
            size: Image size (width, height)
            format: Image format
            
        Returns:
            Path to the created image
        """
        # Create image with white background
        image = Image.new('RGB', size, color='white')
        draw = ImageDraw.Draw(image)
        
        # Try to use a default font, fallback to basic font
        try:
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            font = ImageFont.load_default()
            
        # Calculate text position (centered)
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        x = (size[0] - text_width) // 2
        y = (size[1] - text_height) // 2
        
        # Draw text
        draw.text((x, y), text, fill='black', font=font)
        
        # Save image
        output_path = self.output_dir / filename
        image.save(output_path, format=format)
        
        logger.info(f"Created test image: {output_path}")
        return output_path
        
    def create_test_images_batch(self, count: int = 5) -> List[Path]:
        """
        Create a batch of test images with different formats and content.
        
        Args:
            count: Number of images to create
            
        Returns:
            List of paths to created images
        """
        images = []
        formats = ['PNG', 'JPEG', 'BMP', 'TIFF']
        
        for i in range(count):
            format_type = formats[i % len(formats)]
            ext = format_type.lower()
            if ext == 'jpeg':
                ext = 'jpg'
                
            text = f"Test Image {i+1}\nFormat: {format_type}\nContent for OCR"
            filename = f"test_image_{i+1}.{ext}"
            
            image_path = self.create_test_image(
                text=text,
                filename=filename,
                size=(500, 300),
                format=format_type
            )
            images.append(image_path)
            
        return images
        
    def create_invalid_files(self) -> List[Path]:
        """
        Create invalid files for testing error handling.
        
        Returns:
            List of paths to invalid files
        """
        invalid_files = []
        
        # Create empty file
        empty_file = self.output_dir / "empty.txt"
        empty_file.touch()
        invalid_files.append(empty_file)
        
        # Create text file with image extension
        fake_image = self.output_dir / "fake_image.jpg"
        fake_image.write_text("This is not an image file")
        invalid_files.append(fake_image)
        
        # Create very large file (if needed for testing)
        # large_file = self.output_dir / "large_file.png"
        # large_file.write_bytes(b'0' * (11 * 1024 * 1024))  # 11MB
        # invalid_files.append(large_file)
        
        return invalid_files

class TestFileProcessor(unittest.TestCase):
    """Test cases for FileProcessor class."""
    
    def setUp(self):
        """Set up test environment."""
        self.processor = FileProcessor()
        self.test_generator = TestDataGenerator("test_temp")
        
    def tearDown(self):
        """Clean up test environment."""
        # Clean up test files
        import shutil
        if Path("test_temp").exists():
            shutil.rmtree("test_temp")
            
    def test_validate_valid_image(self):
        """Test validation of valid image files."""
        # Create test image
        image_path = self.test_generator.create_test_image()
        
        # Validate
        result = self.processor.validate_file(image_path)
        
        self.assertTrue(result['is_valid'])
        self.assertEqual(result['extension'], '.png')
        self.assertEqual(result['mime_type'], 'image/png')
        self.assertGreater(result['size'], 0)
        
    def test_validate_invalid_file(self):
        """Test validation of invalid files."""
        # Create invalid file
        invalid_files = self.test_generator.create_invalid_files()
        
        for invalid_file in invalid_files:
            with self.assertRaises(FileValidationError):
                self.processor.validate_file(invalid_file)
                
    def test_discover_images_in_directory(self):
        """Test image discovery in directory."""
        # Create test images
        test_images = self.test_generator.create_test_images_batch(3)
        
        # Discover images
        discovered = self.processor.discover_images_in_directory(
            self.test_generator.output_dir, recursive=False
        )
        
        self.assertGreaterEqual(len(discovered), 3)
        
    def test_batch_validation(self):
        """Test batch file validation."""
        # Create mix of valid and invalid files
        valid_images = self.test_generator.create_test_images_batch(2)
        invalid_files = self.test_generator.create_invalid_files()
        
        all_files = valid_images + invalid_files
        
        # Batch validate
        valid, invalid = self.processor.batch_validate_files(all_files)
        
        self.assertGreaterEqual(len(valid), 2)
        self.assertGreaterEqual(len(invalid), 1)

class TestA2AOCRClient(unittest.TestCase):
    """Test cases for A2AOCRClient class."""
    
    def setUp(self):
        """Set up test environment."""
        self.client = A2AOCRClient(server_url="http://localhost:8003")
        self.test_generator = TestDataGenerator("test_temp")
        
    def tearDown(self):
        """Clean up test environment."""
        import shutil
        if Path("test_temp").exists():
            shutil.rmtree("test_temp")
            
    @patch('a2a_ocr_client.A2AClient')
    async def test_process_images_mock(self, mock_a2a_client):
        """Test image processing with mocked A2A client."""
        # Create test image
        image_path = self.test_generator.create_test_image()
        
        # Mock A2A response
        mock_response = MagicMock()
        mock_response.root.result.model_dump.return_value = {
            'status': 'success',
            'results': [{'text': 'Test OCR Text', 'confidence': 0.95}]
        }
        
        mock_a2a_client.return_value.send_message = AsyncMock(return_value=mock_response)
        
        # Process image
        async with self.client as client:
            result = await client.process_images([image_path])
            
        self.assertIn('status', result)
        
    def test_file_validation(self):
        """Test file validation in client."""
        # Create test image
        image_path = self.test_generator.create_test_image()
        
        # Validate
        result = self.client.validate_file(image_path)
        
        self.assertTrue(result['is_valid'])

class TestFastAPIServer:
    """Test cases for FastAPI server."""
    
    def __init__(self):
        self.app = create_app(ocr_server_url="http://localhost:8003")
        self.test_generator = TestDataGenerator("test_temp")
        
    async def test_health_endpoint(self):
        """Test health check endpoint."""
        async with httpx.AsyncClient(app=self.app, base_url="http://test") as client:
            response = await client.get("/health")
            
        assert response.status_code == 200
        data = response.json()
        assert data['status'] == 'healthy'
        
    async def test_upload_endpoint_mock(self):
        """Test file upload endpoint with mocked OCR processing."""
        # Create test image
        image_path = self.test_generator.create_test_image()
        
        # Prepare multipart form data
        with open(image_path, 'rb') as f:
            files = {'files': ('test.png', f, 'image/png')}
            
            async with httpx.AsyncClient(app=self.app, base_url="http://test") as client:
                with patch('fastapi_ocr_server.A2AOCRClient') as mock_client:
                    # Mock the A2A client response
                    mock_instance = AsyncMock()
                    mock_instance.process_images.return_value = {
                        'status': 'success',
                        'results': [{'text': 'Test OCR Text'}]
                    }
                    mock_client.return_value.__aenter__.return_value = mock_instance
                    
                    response = await client.post("/upload", files=files)
                    
        assert response.status_code == 200
        data = response.json()
        assert data['status'] == 'success'

async def run_integration_tests():
    """Run integration tests."""
    logger.info("Running integration tests...")
    
    # Test FastAPI server
    server_tests = TestFastAPIServer()
    await server_tests.test_health_endpoint()
    await server_tests.test_upload_endpoint_mock()
    
    logger.info("Integration tests completed successfully!")

def run_unit_tests():
    """Run unit tests."""
    logger.info("Running unit tests...")
    
    # Run file processor tests
    suite = unittest.TestLoader().loadTestsFromTestCase(TestFileProcessor)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Run A2A client tests
    suite = unittest.TestLoader().loadTestsFromTestCase(TestA2AOCRClient)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    logger.info("Unit tests completed!")
    return result.wasSuccessful()

def generate_test_data():
    """Generate test data for manual testing."""
    logger.info("Generating test data...")
    
    generator = TestDataGenerator("test_data")
    
    # Create various test images
    test_images = generator.create_test_images_batch(10)
    logger.info(f"Created {len(test_images)} test images")
    
    # Create invalid files for error testing
    invalid_files = generator.create_invalid_files()
    logger.info(f"Created {len(invalid_files)} invalid files for error testing")
    
    logger.info("Test data generation completed!")
    logger.info(f"Test files created in: {generator.output_dir}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test A2A OCR System")
    parser.add_argument("--run-all", action="store_true", help="Run all tests")
    parser.add_argument("--test-client", action="store_true", help="Test A2A client only")
    parser.add_argument("--test-server", action="store_true", help="Test FastAPI server only")
    parser.add_argument("--generate-test-data", action="store_true", help="Generate test data")
    parser.add_argument("--integration", action="store_true", help="Run integration tests")
    
    args = parser.parse_args()
    
    if args.generate_test_data or args.run_all:
        generate_test_data()
        
    if args.test_client or args.run_all:
        success = run_unit_tests()
        if not success:
            exit(1)
            
    if args.integration or args.run_all:
        asyncio.run(run_integration_tests())
        
    if not any([args.run_all, args.test_client, args.test_server, args.generate_test_data, args.integration]):
        parser.print_help()
