import json
import os

from  google.adk.agents.llm_agent import Agent
from  google.adk.tools.mcp_tool.mcp_toolset import MCPToolset
from  google.adk.tools.mcp_tool.client.stdio import StdioServerParameters


from ocr_agent.prompt import OCR_PROMPT


def create_ocr_agent() -> Agent:
    # Enhanced environment for MCP OCR tool
    mcp_env = os.environ.copy()

    # Add any specific environment variables needed for OCR
    # This can help with Tesseract path issues
    mcp_env.update({
        "TESSDATA_PREFIX": os.environ.get("TESSDATA_PREFIX", ""),
        "PATH": os.environ.get("PATH", "")
    })

    return Agent(
        name="ocr_agent",
        model="gemini-2.0-flash",
        description="Agent that processes images and extracts content using OCR capabilities.",
        instruction=OCR_PROMPT,
        tools=[
            MCPToolset(
                connection_params=StdioServerParameters(
                    command="python",
                    args=["-m", "mcp_ocr"],
                    env=mcp_env,
                    # Add timeout and other parameters to help with connection issues
                    cwd=None  # Use current working directory
                )
            ),
        ],
    )

root_agent = create_ocr_agent()
